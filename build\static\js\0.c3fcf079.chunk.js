"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[0],{7012:(t,e,a)=>{a.d(e,{A:()=>c});var r=a(5043);function s(t,e){let{title:a,titleId:s,...c}=t;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":s},c),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const c=r.forwardRef(s)},7098:(t,e,a)=>{a.d(e,{A:()=>c});var r=a(5043);function s(t,e){let{title:a,titleId:s,...c}=t;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":s},c),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const c=r.forwardRef(s)},8736:(t,e,a)=>{a.d(e,{h:()=>p});var r=a(5043),s=a(1568),c=a(4703),i=a(8479);const u=t=>({id:t.id,name:t.name||"",email:t.email,phone:t.phone||"",address:t.address||"",status:"banned"===t.status?"banned":"active",verificationStatus:t.verificationStatus,categories:t.categories?[t.categories]:[],contactPerson:t.contactPerson,logo:t.image||"",website:""}),n=t=>({id:t.id,name:t.name,sku:t.sku,category:t.category,price:t.price,stock:t.stock,minimumStock:10,status:t.status,description:t.description||"",image:t.image||"",images:t.image?[t.image]:[],attributes:[],variants:[],createdAt:t.createdDate||(new Date).toISOString(),updatedAt:t.updatedDate||(new Date).toISOString()}),l={getSuppliers:async t=>{try{const e=await s.A.get("/suppliers",{params:t});if(e.data&&"success"in e.data&&e.data.success){const t=e.data;if(t.data&&"suppliers"in t.data){return t.data.suppliers.map(u)}if(Array.isArray(t.data)){return t.data.map(u)}return[]}return(Array.isArray(e.data)?e.data:[]).map(u)}catch(e){throw(0,c.hS)(e)}},getSupplierById:async t=>{try{const e=await s.A.get(`/suppliers/${t}`);if(e.data&&"success"in e.data&&e.data.success)return u(e.data.data);{const t=e.data;return u(t)}}catch(e){throw(0,c.hS)(e)}},createSupplier:async t=>{try{const e={email:t.email,password:t.password,contactPerson:t.contactPerson||t.supplierName||"",name:t.name||t.supplierName,phone:t.phone,address:t.address,categories:t.categories||t.businessType,image:t.image},a=await s.A.post("/suppliers",e);if(a.data&&"success"in a.data&&a.data.success)return u(a.data.data);{const t=a.data;return u(t)}}catch(e){throw(0,c.hS)(e)}},updateSupplier:async(t,e)=>{try{const a={};e.supplierName&&(a.name=e.supplierName),e.email&&(a.email=e.email),e.phone&&(a.phone=e.phone),e.address&&(a.address=e.address),e.businessType&&(a.categories=[e.businessType]),e.image&&(a.image=e.image);const r=await s.A.put(`/suppliers/${t}`,a);return i.lg.update(r,"supplier",t)}catch(a){throw(0,c.hS)(a)}},deleteSupplier:async t=>{try{const e=await s.A.delete(`/suppliers/${t}`);return i.lg.delete(e,"supplier",t)}catch(e){throw(0,c.hS)(e)}},updateVerificationStatus:async(t,e)=>{try{const a=await s.A.put(`/suppliers/${t}/verification-status`,{verificationStatus:e});if(a.data&&"success"in a.data&&a.data.success)return u(a.data.data);{const t=a.data;return u(t)}}catch(a){throw(0,c.hS)(a)}},getSuppliersByVerificationStatus:async t=>{try{const e=await s.A.get("/suppliers",{params:{verificationStatus:t}});if(!e.data)throw new Error(`No suppliers found with status: ${t}`);return e.data}catch(e){throw(0,c.hS)(e)}},getSupplierProducts:async(t,e)=>{try{const a=await s.A.get(`/suppliers/${t}/products`,{params:e});if(a.data&&"success"in a.data&&a.data.success){const t=a.data;if(t.data&&"products"in t.data){return t.data.products.map(n)}if(Array.isArray(t.data)){return t.data.map(n)}return[]}return(Array.isArray(a.data)?a.data:[]).map(n)}catch(a){throw(0,c.hS)(a)}},getProductById:async t=>{try{const e=await s.A.get(`/products/${t}`);if(!e.data)throw new Error(`No product data received for ID: ${t}`);return e.data}catch(e){throw(0,c.hS)(e)}},updateProduct:async(t,e)=>{try{const a=await s.A.put(`/products/${t}`,e);if(!a.data)throw new Error(`Failed to update product ${t}`);return a.data}catch(a){throw(0,c.hS)(a)}},uploadProductImages:async(t,e)=>{try{const a=new FormData;e.forEach(((t,e)=>{a.append(`images[${e}]`,t)}));const r=await s.A.post(`/products/${t}/upload-images`,a,{headers:{"Content-Type":"multipart/form-data"}});if(!r.data)throw new Error("Failed to upload product images");return r.data}catch(a){throw(0,c.hS)(a)}},getSupplierDocuments:async(t,e)=>{try{const a=await s.A.get(`/suppliers/${t}/documents`,{params:e});return a.data?a.data:[]}catch(r){var a;if(404===(null===(a=r.response)||void 0===a?void 0:a.status)||404===r.status)return console.warn(`[TEMP] Documents endpoint not yet implemented for supplier ${t}`),[];throw(0,c.hS)(r)}},getSupplierAnalytics:async(t,e)=>{try{const a=await s.A.get(`/suppliers/${t}/analytics`,{params:e});return a.data?a.data:null}catch(r){var a;if(404===(null===(a=r.response)||void 0===a?void 0:a.status)||404===r.status)return console.warn(`[TEMP] Analytics endpoint not yet implemented for supplier ${t}`),null;throw(0,c.hS)(r)}},uploadSupplierImage:async(t,e)=>{try{const a=new FormData;a.append("image",e);const r=await s.A.post(`/suppliers/${t}/upload-image`,a,{headers:{"Content-Type":"multipart/form-data"}});if(!r.data)throw new Error("Failed to upload supplier image");return r.data}catch(a){throw(0,c.hS)(a)}},banSupplier:async t=>{try{const e=await s.A.put(`/suppliers/${t}/ban`,{status:"banned"});if(e.data&&"success"in e.data&&e.data.success)return u(e.data.data);{const t=e.data;return u(t)}}catch(e){throw(0,c.hS)(e)}},unbanSupplier:async t=>{try{const e=await s.A.put(`/suppliers/${t}/unban`);if(e.data&&"success"in e.data&&e.data.success)return u(e.data.data);{const t=e.data;return u(t)}}catch(e){throw(0,c.hS)(e)}}};var o=a(9705);const p=()=>{const[t,e]=(0,r.useState)([]),[a,s]=(0,r.useState)(!1),[c,i]=(0,r.useState)(null),{showNotification:u}=(0,o.A)(),n=(0,r.useRef)(u),p=(0,r.useRef)(!1);(0,r.useEffect)((()=>{n.current=u}));const d=(0,r.useCallback)((async()=>{s(!0),i(null);try{const t=await l.getSuppliers();e(t)}catch(t){i(t),n.current({type:"error",title:"Error",message:"Failed to fetch suppliers"})}finally{s(!1)}}),[]),y=(0,r.useCallback)((async function(t){let a=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];s(!0),i(null);try{const r=await l.createSupplier(t);return e((t=>[...t,r])),a&&n.current({type:"success",title:"Success",message:"Supplier created successfully"}),r}catch(r){throw i(r),a&&n.current({type:"error",title:"Error",message:"Failed to create supplier"}),r}finally{s(!1)}}),[]),h=(0,r.useCallback)((async(t,a)=>{s(!0),i(null);try{const r=await l.updateSupplier(t,a);return e((e=>e.map((e=>e.id===t?r:e)))),n.current({type:"success",title:"Success",message:"Supplier updated successfully"}),r}catch(r){throw i(r),n.current({type:"error",title:"Error",message:"Failed to update supplier"}),r}finally{s(!1)}}),[]),m=(0,r.useCallback)((async t=>{s(!0),i(null);try{await l.deleteSupplier(t),e((e=>e.filter((e=>e.id!==t))))}catch(a){throw i(a),n.current({type:"error",title:"Error",message:"Failed to delete supplier"}),a}finally{s(!1)}}),[]),g=(0,r.useCallback)((async t=>{s(!0),i(null);try{return await l.getSupplierById(t)}catch(e){throw i(e),n.current({type:"error",title:"Error",message:"Failed to fetch supplier details"}),e}finally{s(!1)}}),[]),w=(0,r.useCallback)((async(t,a)=>{s(!0),i(null);try{const r=await l.updateVerificationStatus(t,a);return e((e=>e.map((e=>e.id===t?r:e)))),n.current({type:"success",title:"Success",message:`Supplier ${"verified"===a?"verified":"set to pending"} successfully`}),r}catch(r){throw i(r),n.current({type:"error",title:"Error",message:"Failed to update supplier verification status"}),r}finally{s(!1)}}),[]);(0,r.useEffect)((()=>{p.current||(p.current=!0,d())}),[]);const f=(0,r.useCallback)((async t=>{s(!0),i(null);try{return await l.getSupplierProducts(t)}catch(e){throw i(e),n.current({type:"error",title:"Error",message:"Failed to fetch supplier products"}),e}finally{s(!1)}}),[]),S=(0,r.useCallback)((async t=>{s(!0),i(null);try{return await l.getProductById(t)}catch(e){throw i(e),n.current({type:"error",title:"Error",message:"Failed to fetch product details"}),e}finally{s(!1)}}),[]),b=(0,r.useCallback)((async(t,e)=>{s(!0),i(null);try{const a=await l.updateProduct(t,e);return n.current({type:"success",title:"Success",message:"Product updated successfully"}),a}catch(a){throw i(a),n.current({type:"error",title:"Error",message:"Failed to update product"}),a}finally{s(!1)}}),[]),v=(0,r.useCallback)((async(t,e)=>{s(!0),i(null);try{const a=await l.uploadProductImages(t,e);return n.current({type:"success",title:"Success",message:"Product images uploaded successfully"}),a}catch(a){throw i(a),n.current({type:"error",title:"Error",message:"Failed to upload product images"}),a}finally{s(!1)}}),[]),A=(0,r.useCallback)((async t=>{try{return await l.getSupplierDocuments(t)}catch(a){var e;const t=a;return 404!==(null===(e=t.response)||void 0===e?void 0:e.status)&&404!==t.status&&(i(a),n.current({type:"error",title:"Error",message:"Failed to fetch supplier documents"})),[]}}),[]),E=(0,r.useCallback)((async t=>{try{return await l.getSupplierAnalytics(t)}catch(a){var e;const t=a;return 404!==(null===(e=t.response)||void 0===e?void 0:e.status)&&404!==t.status&&(i(a),n.current({type:"error",title:"Error",message:"Failed to fetch supplier analytics"})),null}}),[]),k=(0,r.useCallback)((async(t,e)=>{s(!0),i(null);try{const a=await l.uploadSupplierImage(t,e);return n.current({type:"success",title:"Success",message:"Supplier image uploaded successfully"}),a}catch(a){throw i(a),n.current({type:"error",title:"Error",message:"Failed to upload supplier image"}),a}finally{s(!1)}}),[]),C=(0,r.useCallback)((async t=>{s(!0),i(null);try{const a=await l.banSupplier(t);return e((e=>e.map((e=>e.id===t?a:e)))),a}catch(a){throw i(a),n.current({type:"error",title:"Error",message:"Failed to ban supplier"}),a}finally{s(!1)}}),[]),F=(0,r.useCallback)((async t=>{s(!0),i(null);try{const a=await l.unbanSupplier(t);return e((e=>e.map((e=>e.id===t?a:e)))),a}catch(a){throw i(a),n.current({type:"error",title:"Error",message:"Failed to unban supplier"}),a}finally{s(!1)}}),[]);return{suppliers:t,isLoading:a,error:c,fetchSuppliers:d,getSupplierById:g,createSupplier:y,createEntity:y,updateSupplier:h,deleteSupplier:m,deleteEntity:m,updateVerificationStatus:w,getSupplierProducts:f,getSupplierDocuments:A,getSupplierAnalytics:E,uploadSupplierImage:k,banSupplier:C,unbanSupplier:F,getProductById:S,updateProduct:b,uploadProductImages:v}}}}]);
//# sourceMappingURL=0.c3fcf079.chunk.js.map