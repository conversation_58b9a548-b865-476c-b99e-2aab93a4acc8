{"version": 3, "file": "static/js/0.c3fcf079.chunk.js", "mappings": "8IACA,SAASA,EAASC,EAIfC,GAAQ,IAJQ,MACjBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,qDAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBN,E,gDCvBlD,SAASmB,EAAWlB,EAIjBC,GAAQ,IAJU,MACnBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,0EAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBa,E,8ECAlD,MAAMC,EAA4BC,IACzB,CACLN,GAAIM,EAAgBN,GACpBO,KAAMD,EAAgBC,MAAQ,GAC9BC,MAAOF,EAAgBE,MACvBC,MAAOH,EAAgBG,OAAS,GAChCC,QAASJ,EAAgBI,SAAW,GACpCC,OAAmC,WAA3BL,EAAgBK,OAAsB,SAAW,SACzDC,mBAAoBN,EAAgBM,mBACpCC,WAAYP,EAAgBO,WAAa,CAACP,EAAgBO,YAAc,GACxEC,cAAeR,EAAgBQ,cAC/BC,KAAMT,EAAgBU,OAAS,GAC/BC,QAAS,KAKPC,EAAmCC,IAAsC,CAC7EnB,GAAImB,EAAenB,GACnBO,KAAMY,EAAeZ,KACrBa,IAAKD,EAAeC,IACpBC,SAAUF,EAAeE,SACzBC,MAAOH,EAAeG,MACtBC,MAAOJ,EAAeI,MACtBC,aAAc,GACdb,OAAQQ,EAAeR,OACvBc,YAAaN,EAAeM,aAAe,GAC3CT,MAAOG,EAAeH,OAAS,GAC/BU,OAAQP,EAAeH,MAAQ,CAACG,EAAeH,OAAS,GACxDW,WAAY,GACZC,SAAU,GACVC,UAAWV,EAAeW,cAAe,IAAIC,MAAOC,cACpDC,UAAWd,EAAee,cAAe,IAAIH,MAAOC,gBAoWtD,EAjW4B,CAI1BG,aAAcC,UACZ,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAA+C,aAAc,CAAEC,WAGhG,GAAIH,EAASI,MAAQ,YAAaJ,EAASI,MAAQJ,EAASI,KAAKC,QAAS,CACxE,MAAMC,EAAkBN,EAASI,KAEjC,GAAIE,EAAgBF,MAAQ,cAAeE,EAAgBF,KAAM,CAE/D,OADyBE,EAAgBF,KAAKG,UACtBC,IAAIxC,EAC9B,CAEK,GAAIyC,MAAMC,QAAQJ,EAAgBF,MAAO,CAE5C,OADuBE,EAAgBF,KACjBI,IAAIxC,EAC5B,CAGE,MAAO,EAEX,CAGE,OADkByC,MAAMC,QAAQV,EAASI,MAAQJ,EAASI,KAA4B,IACrEI,IAAIxC,EAEzB,CAAE,MAAO2C,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFE,gBAAiBd,UACf,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAyC,cAAcvC,KAGxF,GAAIqC,EAASI,MAAQ,YAAaJ,EAASI,MAAQJ,EAASI,KAAKC,QAC/D,OAAOrC,EAAyBgC,EAASI,KAAKA,MACzC,CAEL,MAAMU,EAAWd,EAASI,KAC1B,OAAOpC,EAAyB8C,EAClC,CACF,CAAE,MAAOH,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFI,eAAgBhB,UACd,IAEE,MAAMiB,EAAU,CACd7C,MAAO8C,EAAa9C,MACpB+C,SAAUD,EAAaC,SACvBzC,cAAewC,EAAaxC,eAAiBwC,EAAaE,cAAgB,GAC1EjD,KAAM+C,EAAa/C,MAAQ+C,EAAaE,aACxC/C,MAAO6C,EAAa7C,MACpBC,QAAS4C,EAAa5C,QACtBG,WAAYyC,EAAazC,YAAcyC,EAAaG,aACpDzC,MAAOsC,EAAatC,OAGhBqB,QAAiBC,EAAAA,EAAUoB,KAA0C,aAAcL,GAGzF,GAAIhB,EAASI,MAAQ,YAAaJ,EAASI,MAAQJ,EAASI,KAAKC,QAC/D,OAAOrC,EAAyBgC,EAASI,KAAKA,MACzC,CAEL,MAAMU,EAAWd,EAASI,KAC1B,OAAOpC,EAAyB8C,EAClC,CACF,CAAE,MAAOH,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFW,eAAgBvB,MAAOpC,EAAYsD,KACjC,IAEE,MAAMD,EAAe,CAAC,EAClBC,EAAaE,eAAcH,EAAQ9C,KAAO+C,EAAaE,cACvDF,EAAa9C,QAAO6C,EAAQ7C,MAAQ8C,EAAa9C,OACjD8C,EAAa7C,QAAO4C,EAAQ5C,MAAQ6C,EAAa7C,OACjD6C,EAAa5C,UAAS2C,EAAQ3C,QAAU4C,EAAa5C,SACrD4C,EAAaG,eAAcJ,EAAQxC,WAAa,CAACyC,EAAaG,eAC9DH,EAAatC,QAAOqC,EAAQrC,MAAQsC,EAAatC,OAErD,MAAMqB,QAAiBC,EAAAA,EAAUsB,IAAc,cAAc5D,IAAMqD,GACnE,OAAOQ,EAAAA,GAAmBC,OAAOzB,EAAU,WAAYrC,EACzD,CAAE,MAAOgD,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFe,eAAgB3B,UACd,IACE,MAAMC,QAAiBC,EAAAA,EAAU0B,OAAO,cAAchE,KACtD,OAAO6D,EAAAA,GAAmBG,OAAO3B,EAAU,WAAYrC,EACzD,CAAE,MAAOgD,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFiB,yBAA0B7B,MAAOpC,EAAYW,KAC3C,IAEE,MAAM0B,QAAiBC,EAAAA,EAAUsB,IAAyC,cAAc5D,wBAA0B,CAChHY,mBAAoBD,IAItB,GAAI0B,EAASI,MAAQ,YAAaJ,EAASI,MAAQJ,EAASI,KAAKC,QAC/D,OAAOrC,EAAyBgC,EAASI,KAAKA,MACzC,CAEL,MAAMU,EAAWd,EAASI,KAC1B,OAAOpC,EAAyB8C,EAClC,CACF,CAAE,MAAOH,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFkB,iCAAkC9B,UAChC,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAgB,aAAc,CAAEC,OAAQ,CAAE5B,mBAAoBD,KAC/F,IAAK0B,EAASI,KACZ,MAAM,IAAI0B,MAAM,mCAAmCxD,KAErD,OAAO0B,EAASI,IAClB,CAAE,MAAOO,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFoB,oBAAqBhC,MAAOiC,EAAoB7B,KAC9C,IACE,MAAMH,QAAiBC,EAAAA,EAAUC,IAAyD,cAAc8B,aAAuB,CAAE7B,WAGjI,GAAIH,EAASI,MAAQ,YAAaJ,EAASI,MAAQJ,EAASI,KAAKC,QAAS,CACxE,MAAMC,EAAkBN,EAASI,KAEjC,GAAIE,EAAgBF,MAAQ,aAAcE,EAAgBF,KAAM,CAE9D,OADwBE,EAAgBF,KAAK6B,SACtBzB,IAAI3B,EAC7B,CAEK,GAAI4B,MAAMC,QAAQJ,EAAgBF,MAAO,CAE5C,OADsBE,EAAgBF,KACjBI,IAAI3B,EAC3B,CAGE,MAAO,EAEX,CAGE,OADiB4B,MAAMC,QAAQV,EAASI,MAAQJ,EAASI,KAAmC,IAC5EI,IAAI3B,EAExB,CAAE,MAAO8B,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFuB,eAAgBnC,UACd,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAqB,aAAaiC,KACnE,IAAKnC,EAASI,KACZ,MAAM,IAAI0B,MAAM,oCAAoCK,KAEtD,OAAOnC,EAASI,IAClB,CAAE,MAAOO,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFyB,cAAerC,MAAOoC,EAAmBE,KACvC,IACE,MAAMrC,QAAiBC,EAAAA,EAAUsB,IAAqB,aAAaY,IAAaE,GAChF,IAAKrC,EAASI,KACZ,MAAM,IAAI0B,MAAM,4BAA4BK,KAE9C,OAAOnC,EAASI,IAClB,CAAE,MAAOO,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMF2B,oBAAqBvC,MAAOoC,EAAmBI,KAC7C,IACE,MAAMC,EAAW,IAAIC,SACrBF,EAAMG,SAAQ,CAACC,EAAMC,KACnBJ,EAASK,OAAO,UAAUD,KAAUD,EAAK,IAG3C,MAAM3C,QAAiBC,EAAAA,EAAUoB,KAA8B,aAAac,kBAA2BK,EAAU,CAC/GM,QAAS,CACP,eAAgB,yBAIpB,IAAK9C,EAASI,KACZ,MAAM,IAAI0B,MAAM,mCAElB,OAAO9B,EAASI,IAClB,CAAE,MAAOO,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAOFoC,qBAAsBhD,MAAOiC,EAAoB7B,KAC/C,IACE,MAAMH,QAAiBC,EAAAA,EAAUC,IAAwB,cAAc8B,cAAwB,CAAE7B,WACjG,OAAKH,EAASI,KAGPJ,EAASI,KAFP,EAGX,CAAE,MAAOO,GAAa,IAADqC,EAEnB,GAA+B,OAAb,QAAdA,EAAArC,EAAMX,gBAAQ,IAAAgD,OAAA,EAAdA,EAAgB1E,SAAmC,MAAjBqC,EAAMrC,OAE1C,OADA2E,QAAQC,KAAK,8DAA8DlB,KACpE,GAET,MAAMpB,EAAAA,EAAAA,IAAeD,EACvB,GAOFwC,qBAAsBpD,MAAOiC,EAAoB7B,KAC/C,IACE,MAAMH,QAAiBC,EAAAA,EAAUC,IAA2B,cAAc8B,cAAwB,CAAE7B,WACpG,OAAKH,EAASI,KAGPJ,EAASI,KAFP,IAGX,CAAE,MAAOO,GAAa,IAADyC,EAEnB,GAA+B,OAAb,QAAdA,EAAAzC,EAAMX,gBAAQ,IAAAoD,OAAA,EAAdA,EAAgB9E,SAAmC,MAAjBqC,EAAMrC,OAE1C,OADA2E,QAAQC,KAAK,8DAA8DlB,KACpE,KAET,MAAMpB,EAAAA,EAAAA,IAAeD,EACvB,GAMF0C,oBAAqBtD,MAAOiC,EAAoBW,KAC9C,IACE,MAAMH,EAAW,IAAIC,SACrBD,EAASK,OAAO,QAASF,GAEzB,MAAM3C,QAAiBC,EAAAA,EAAUoB,KAA2B,cAAcW,iBAA2BQ,EAAU,CAC7GM,QAAS,CACP,eAAgB,yBAIpB,IAAK9C,EAASI,KACZ,MAAM,IAAI0B,MAAM,mCAElB,OAAO9B,EAASI,IAClB,CAAE,MAAOO,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMF2C,YAAavD,UACX,IACE,MAAMC,QAAiBC,EAAAA,EAAUsB,IAAyC,cAAc5D,QAAU,CAAEW,OAAQ,WAG5G,GAAI0B,EAASI,MAAQ,YAAaJ,EAASI,MAAQJ,EAASI,KAAKC,QAC/D,OAAOrC,EAAyBgC,EAASI,KAAKA,MACzC,CAEL,MAAMU,EAAWd,EAASI,KAC1B,OAAOpC,EAAyB8C,EAClC,CACF,CAAE,MAAOH,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMF4C,cAAexD,UACb,IACE,MAAMC,QAAiBC,EAAAA,EAAUsB,IAAyC,cAAc5D,WAGxF,GAAIqC,EAASI,MAAQ,YAAaJ,EAASI,MAAQJ,EAASI,KAAKC,QAC/D,OAAOrC,EAAyBgC,EAASI,KAAKA,MACzC,CAEL,MAAMU,EAAWd,EAASI,KAC1B,OAAOpC,EAAyB8C,EAClC,CACF,CAAE,MAAOH,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,I,cC7YG,MAAM6C,EAAeA,KAC1B,MAAOjD,EAAWkD,IAAgBC,EAAAA,EAAAA,UAAqB,KAChDC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpC/C,EAAOkD,IAAYH,EAAAA,EAAAA,UAAuB,OAC3C,iBAAEI,IAAqBC,EAAAA,EAAAA,KAGvBC,GAAsBC,EAAAA,EAAAA,QAAOH,GAC7BI,GAAoBD,EAAAA,EAAAA,SAAO,IAGjCE,EAAAA,EAAAA,YAAU,KACRH,EAAoBI,QAAUN,CAAgB,IAIhD,MAAMO,GAAiBC,EAAAA,EAAAA,cAAYvE,UACjC6D,GAAa,GACbC,EAAS,MACT,IACE,MAAMzD,QAAamE,EAAazE,eAChC2D,EAAarD,EACf,CAAE,MAAOoE,GACPX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,6BAEb,CAAC,QACCd,GAAa,EACf,IACC,IAGG7C,GAAiBuD,EAAAA,EAAAA,cAAYvE,eAAOkB,GAAuE,IAAvC0D,IAA0BC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,KAAAA,UAAA,GAClGhB,GAAa,GACbC,EAAS,MACT,IACE,MAAMkB,QAAoBR,EAAaxD,eAAeE,GAStD,OARAwC,GAAauB,GAAiB,IAAIA,EAAeD,KAC7CJ,GACFX,EAAoBI,QAAQ,CAC1BK,KAAM,UACN1H,MAAO,UACP2H,QAAS,kCAGNK,CACT,CAAE,MAAOP,GAWP,MAVAX,EAASW,GAGLG,GACFX,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,8BAGPF,CACR,CAAC,QACCZ,GAAa,EACf,CACF,GAAG,IAGGtC,GAAiBgD,EAAAA,EAAAA,cAAYvE,MAAOpC,EAAYsD,KACpD2C,GAAa,GACbC,EAAS,MACT,IACE,MAAMoB,QAAwBV,EAAajD,eAAe3D,EAAIsD,GAS9D,OARAwC,GAAauB,GACXA,EAAcxE,KAAIM,GAAYA,EAASnD,KAAOA,EAAKsH,EAAkBnE,MAEvEkD,EAAoBI,QAAQ,CAC1BK,KAAM,UACN1H,MAAO,UACP2H,QAAS,kCAEJO,CACT,CAAE,MAAOT,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,8BAELF,CACR,CAAC,QACCZ,GAAa,EACf,IACC,IAGGlC,GAAiB4C,EAAAA,EAAAA,cAAYvE,UACjC6D,GAAa,GACbC,EAAS,MACT,UACQU,EAAa7C,eAAe/D,GAClC8F,GAAauB,GAAiBA,EAAcE,QAAOpE,GAAYA,EAASnD,KAAOA,KAEjF,CAAE,MAAO6G,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,8BAELF,CACR,CAAC,QACCZ,GAAa,EACf,IACC,IAGG/C,GAAkByD,EAAAA,EAAAA,cAAYvE,UAClC6D,GAAa,GACbC,EAAS,MACT,IAEE,aADuBU,EAAa1D,gBAAgBlD,EAEtD,CAAE,MAAO6G,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,qCAELF,CACR,CAAC,QACCZ,GAAa,EACf,IACC,IAGGhC,GAA2B0C,EAAAA,EAAAA,cAAYvE,MAAOpC,EAAYW,KAC9DsF,GAAa,GACbC,EAAS,MACT,IACE,MAAMoB,QAAwBV,EAAa3C,yBAAyBjE,EAAIW,GASxE,OARAmF,GAAauB,GACXA,EAAcxE,KAAIM,GAAYA,EAASnD,KAAOA,EAAKsH,EAAkBnE,MAEvEkD,EAAoBI,QAAQ,CAC1BK,KAAM,UACN1H,MAAO,UACP2H,QAAS,YAAuB,aAAXpG,EAAwB,WAAa,kCAErD2G,CACT,CAAE,MAAOT,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,kDAELF,CACR,CAAC,QACCZ,GAAa,EACf,IACC,KAGHO,EAAAA,EAAAA,YAAU,KACHD,EAAkBE,UACrBF,EAAkBE,SAAU,EAC5BC,IACF,GACC,IAGH,MAAMtC,GAAsBuC,EAAAA,EAAAA,cAAYvE,UACtC6D,GAAa,GACbC,EAAS,MACT,IAEE,aADuBU,EAAaxC,oBAAoBC,EAE1D,CAAE,MAAOwC,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,sCAELF,CACR,CAAC,QACCZ,GAAa,EACf,IACC,IAGG1B,GAAiBoC,EAAAA,EAAAA,cAAYvE,UACjC6D,GAAa,GACbC,EAAS,MACT,IAEE,aADsBU,EAAarC,eAAeC,EAEpD,CAAE,MAAOqC,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,oCAELF,CACR,CAAC,QACCZ,GAAa,EACf,IACC,IAGGxB,GAAgBkC,EAAAA,EAAAA,cAAYvE,MAAOoC,EAAmBE,KAC1DuB,GAAa,GACbC,EAAS,MACT,IACE,MAAMsB,QAAuBZ,EAAanC,cAAcD,EAAWE,GAMnE,OALA2B,EAAoBI,QAAQ,CAC1BK,KAAM,UACN1H,MAAO,UACP2H,QAAS,iCAEJS,CACT,CAAE,MAAOX,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,6BAELF,CACR,CAAC,QACCZ,GAAa,EACf,IACC,IAGGtB,GAAsBgC,EAAAA,EAAAA,cAAYvE,MAAOoC,EAAmBI,KAChEqB,GAAa,GACbC,EAAS,MACT,IACE,MAAMuB,QAAeb,EAAajC,oBAAoBH,EAAWI,GAMjE,OALAyB,EAAoBI,QAAQ,CAC1BK,KAAM,UACN1H,MAAO,UACP2H,QAAS,yCAEJU,CACT,CAAE,MAAOZ,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,oCAELF,CACR,CAAC,QACCZ,GAAa,EACf,IACC,IAGGb,GAAuBuB,EAAAA,EAAAA,cAAYvE,UACvC,IAEE,aADwBwE,EAAaxB,qBAAqBf,EAE5D,CAAE,MAAOwC,GAAM,IAADxB,EAEZ,MAAMrC,EAAQ6D,EAUd,OAT+B,OAAb,QAAdxB,EAAArC,EAAMX,gBAAQ,IAAAgD,OAAA,EAAdA,EAAgB1E,SAAmC,MAAjBqC,EAAMrC,SAC1CuF,EAASW,GACTR,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,wCAIN,EACT,IACC,IAGGvB,GAAuBmB,EAAAA,EAAAA,cAAYvE,UACvC,IAEE,aADwBwE,EAAapB,qBAAqBnB,EAE5D,CAAE,MAAOwC,GAAM,IAADpB,EAEZ,MAAMzC,EAAQ6D,EAUd,OAT+B,OAAb,QAAdpB,EAAAzC,EAAMX,gBAAQ,IAAAoD,OAAA,EAAdA,EAAgB9E,SAAmC,MAAjBqC,EAAMrC,SAC1CuF,EAASW,GACTR,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,wCAIN,IACT,IACC,IAGGrB,GAAsBiB,EAAAA,EAAAA,cAAYvE,MAAOiC,EAAoBW,KACjEiB,GAAa,GACbC,EAAS,MACT,IACE,MAAMuB,QAAeb,EAAalB,oBAAoBrB,EAAYW,GAMlE,OALAqB,EAAoBI,QAAQ,CAC1BK,KAAM,UACN1H,MAAO,UACP2H,QAAS,yCAEJU,CACT,CAAE,MAAOZ,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,oCAELF,CACR,CAAC,QACCZ,GAAa,EACf,IACC,IAGGN,GAAcgB,EAAAA,EAAAA,cAAYvE,UAC9B6D,GAAa,GACbC,EAAS,MACT,IACE,MAAMwB,QAAuBd,EAAajB,YAAY3F,GAKtD,OAJA8F,GAAauB,GACXA,EAAcxE,KAAIM,GAAYA,EAASnD,KAAOA,EAAK0H,EAAiBvE,MAG/DuE,CACT,CAAE,MAAOb,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,2BAELF,CACR,CAAC,QACCZ,GAAa,EACf,IACC,IAGGL,GAAgBe,EAAAA,EAAAA,cAAYvE,UAChC6D,GAAa,GACbC,EAAS,MACT,IACE,MAAMyB,QAAyBf,EAAahB,cAAc5F,GAK1D,OAJA8F,GAAauB,GACXA,EAAcxE,KAAIM,GAAYA,EAASnD,KAAOA,EAAK2H,EAAmBxE,MAGjEwE,CACT,CAAE,MAAOd,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BK,KAAM,QACN1H,MAAO,QACP2H,QAAS,6BAELF,CACR,CAAC,QACCZ,GAAa,EACf,IACC,IAEH,MAAO,CACLrD,YACAoD,YACAhD,QACA0D,iBACAxD,kBACAE,iBACAwE,aAAcxE,EACdO,iBACAI,iBACA8D,aAAc9D,EACdE,2BACAG,sBACAgB,uBACAI,uBACAE,sBACAC,cACAC,gBAEArB,iBACAE,gBACAE,sBACD,C", "sources": ["../node_modules/@heroicons/react/24/outline/esm/ClockIcon.js", "../node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js", "features/suppliers/api/suppliersApi.ts", "features/suppliers/hooks/useSuppliers.ts"], "sourcesContent": ["import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction XCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(XCircleIcon);\nexport default ForwardRef;", "/**\r\n * Suppliers API Service\r\n *\r\n * This file provides methods for interacting with the suppliers API endpoints.\r\n */\r\n\r\nimport apiClient from '../../../api';\r\nimport { handleApiError } from '../../../utils/errorHandling';\r\nimport { responseValidators } from '../../../utils/apiHelpers';\r\nimport type {\r\n  Supplier,\r\n  SupplierFormData,\r\n  SupplierProduct,\r\n  SupplierDocument,\r\n  SupplierAnalyticsData,\r\n  BackendSupplier,\r\n  ApiResponseWrapper,\r\n  SuppliersListResponse,\r\n  SupplierQueryParams,\r\n  SupplierProductsResponse,\r\n  BackendSupplierProduct\r\n} from '../types';\r\n\r\n// Helper function to transform backend supplier to frontend format\r\nconst transformBackendSupplier = (backendSupplier: BackendSupplier): Supplier => {\r\n  return {\r\n    id: backendSupplier.id,\r\n    name: backendSupplier.name || '',\r\n    email: backendSupplier.email,\r\n    phone: backendSupplier.phone || '',\r\n    address: backendSupplier.address || '',\r\n    status: backendSupplier.status === 'banned' ? 'banned' : 'active',\r\n    verificationStatus: backendSupplier.verificationStatus,\r\n    categories: backendSupplier.categories ? [backendSupplier.categories] : [],\r\n    contactPerson: backendSupplier.contactPerson,\r\n    logo: backendSupplier.image || '',\r\n    website: ''\r\n  };\r\n};\r\n\r\n// Helper function to transform backend supplier product to frontend format\r\nconst transformBackendSupplierProduct = (backendProduct: BackendSupplierProduct): SupplierProduct => ({\r\n  id: backendProduct.id,\r\n  name: backendProduct.name,\r\n  sku: backendProduct.sku,\r\n  category: backendProduct.category,\r\n  price: backendProduct.price,\r\n  stock: backendProduct.stock,\r\n  minimumStock: 10, // Default value since backend doesn't provide this\r\n  status: backendProduct.status,\r\n  description: backendProduct.description || '',\r\n  image: backendProduct.image || '',\r\n  images: backendProduct.image ? [backendProduct.image] : [],\r\n  attributes: [],\r\n  variants: [],\r\n  createdAt: backendProduct.createdDate || new Date().toISOString(),\r\n  updatedAt: backendProduct.updatedDate || new Date().toISOString()\r\n});\r\n\r\nexport const suppliersApi = {\r\n  /**\r\n   * Get all suppliers with pagination and filtering\r\n   */\r\n  getSuppliers: async (params?: SupplierQueryParams): Promise<Supplier[]> => {\r\n    try {\r\n      const response = await apiClient.get<SuppliersListResponse | BackendSupplier[]>('/suppliers', { params });\r\n\r\n      // Handle wrapped response format\r\n      if (response.data && 'success' in response.data && response.data.success) {\r\n        const wrappedResponse = response.data as SuppliersListResponse;\r\n        // Check if data.data has suppliers array (new format)\r\n        if (wrappedResponse.data && 'suppliers' in wrappedResponse.data) {\r\n          const backendSuppliers = wrappedResponse.data.suppliers;\r\n          return backendSuppliers.map(transformBackendSupplier);\r\n        }\r\n        // Fallback: data.data is directly an array (old format)\r\n        else if (Array.isArray(wrappedResponse.data)) {\r\n          const suppliersArray = wrappedResponse.data as BackendSupplier[];\r\n          return suppliersArray.map(transformBackendSupplier);\r\n        }\r\n        // Fallback: empty array if no suppliers found\r\n        else {\r\n          return [];\r\n        }\r\n      } else {\r\n        // Fallback for non-wrapped responses (legacy support)\r\n        const suppliers = Array.isArray(response.data) ? response.data as BackendSupplier[] : [];\r\n        return suppliers.map(transformBackendSupplier);\r\n      }\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get a supplier by ID\r\n   */\r\n  getSupplierById: async (id: string): Promise<Supplier> => {\r\n    try {\r\n      const response = await apiClient.get<ApiResponseWrapper<BackendSupplier>>(`/suppliers/${id}`);\r\n\r\n      // Handle wrapped response format\r\n      if (response.data && 'success' in response.data && response.data.success) {\r\n        return transformBackendSupplier(response.data.data);\r\n      } else {\r\n        // Fallback for non-wrapped responses (legacy support)\r\n        const supplier = response.data as unknown as BackendSupplier;\r\n        return transformBackendSupplier(supplier);\r\n      }\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Create a new supplier\r\n   */\r\n  createSupplier: async (supplierData: SupplierFormData): Promise<Supplier> => {\r\n    try {\r\n      // Transform form data to match backend API requirements\r\n      const apiData = {\r\n        email: supplierData.email, // Required\r\n        password: supplierData.password, // Required\r\n        contactPerson: supplierData.contactPerson || supplierData.supplierName || '', // Required\r\n        name: supplierData.name || supplierData.supplierName, // Optional business name\r\n        phone: supplierData.phone, // Optional\r\n        address: supplierData.address, // Optional\r\n        categories: supplierData.categories || supplierData.businessType, // Optional single category\r\n        image: supplierData.image // Optional base64 encoded image\r\n      };\r\n\r\n      const response = await apiClient.post<ApiResponseWrapper<BackendSupplier>>('/suppliers', apiData);\r\n\r\n      // Handle wrapped response format\r\n      if (response.data && 'success' in response.data && response.data.success) {\r\n        return transformBackendSupplier(response.data.data);\r\n      } else {\r\n        // Fallback for non-wrapped responses (legacy support)\r\n        const supplier = response.data as unknown as BackendSupplier;\r\n        return transformBackendSupplier(supplier);\r\n      }\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update a supplier\r\n   */\r\n  updateSupplier: async (id: string, supplierData: Partial<SupplierFormData>): Promise<Supplier> => {\r\n    try {\r\n      // Transform form data to match API expectations\r\n      const apiData: any = {};\r\n      if (supplierData.supplierName) apiData.name = supplierData.supplierName;\r\n      if (supplierData.email) apiData.email = supplierData.email;\r\n      if (supplierData.phone) apiData.phone = supplierData.phone;\r\n      if (supplierData.address) apiData.address = supplierData.address;\r\n      if (supplierData.businessType) apiData.categories = [supplierData.businessType];\r\n      if (supplierData.image) apiData.image = supplierData.image;\r\n\r\n      const response = await apiClient.put<Supplier>(`/suppliers/${id}`, apiData);\r\n      return responseValidators.update(response, 'supplier', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete a supplier\r\n   */\r\n  deleteSupplier: async (id: string): Promise<void> => {\r\n    try {\r\n      const response = await apiClient.delete(`/suppliers/${id}`);\r\n      return responseValidators.delete(response, 'supplier', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update supplier verification status\r\n   */\r\n  updateVerificationStatus: async (id: string, status: 'verified' | 'pending'): Promise<Supplier> => {\r\n    try {\r\n      // Use the correct backend endpoint\r\n      const response = await apiClient.put<ApiResponseWrapper<BackendSupplier>>(`/suppliers/${id}/verification-status`, {\r\n        verificationStatus: status\r\n      });\r\n\r\n      // Handle wrapped response format\r\n      if (response.data && 'success' in response.data && response.data.success) {\r\n        return transformBackendSupplier(response.data.data);\r\n      } else {\r\n        // Fallback for non-wrapped responses (legacy support)\r\n        const supplier = response.data as unknown as BackendSupplier;\r\n        return transformBackendSupplier(supplier);\r\n      }\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get suppliers by verification status\r\n   */\r\n  getSuppliersByVerificationStatus: async (status: 'verified' | 'pending' | 'rejected'): Promise<Supplier[]> => {\r\n    try {\r\n      const response = await apiClient.get<Supplier[]>('/suppliers', { params: { verificationStatus: status } });\r\n      if (!response.data) {\r\n        throw new Error(`No suppliers found with status: ${status}`);\r\n      }\r\n      return response.data;\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get supplier products with pagination\r\n   */\r\n  getSupplierProducts: async (supplierId: string, params?: { page?: number; limit?: number }): Promise<SupplierProduct[]> => {\r\n    try {\r\n      const response = await apiClient.get<SupplierProductsResponse | BackendSupplierProduct[]>(`/suppliers/${supplierId}/products`, { params });\r\n\r\n      // Handle wrapped response format\r\n      if (response.data && 'success' in response.data && response.data.success) {\r\n        const wrappedResponse = response.data as SupplierProductsResponse;\r\n        // Check if data.data has products array (new format)\r\n        if (wrappedResponse.data && 'products' in wrappedResponse.data) {\r\n          const backendProducts = wrappedResponse.data.products;\r\n          return backendProducts.map(transformBackendSupplierProduct);\r\n        }\r\n        // Fallback: data.data is directly an array (old format)\r\n        else if (Array.isArray(wrappedResponse.data)) {\r\n          const productsArray = wrappedResponse.data as BackendSupplierProduct[];\r\n          return productsArray.map(transformBackendSupplierProduct);\r\n        }\r\n        // Fallback: empty array if no products found\r\n        else {\r\n          return [];\r\n        }\r\n      } else {\r\n        // Fallback for non-wrapped responses (legacy support)\r\n        const products = Array.isArray(response.data) ? response.data as BackendSupplierProduct[] : [];\r\n        return products.map(transformBackendSupplierProduct);\r\n      }\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get a single product by ID\r\n   */\r\n  getProductById: async (productId: string): Promise<SupplierProduct> => {\r\n    try {\r\n      const response = await apiClient.get<SupplierProduct>(`/products/${productId}`);\r\n      if (!response.data) {\r\n        throw new Error(`No product data received for ID: ${productId}`);\r\n      }\r\n      return response.data;\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update a product\r\n   */\r\n  updateProduct: async (productId: string, productData: Partial<SupplierProduct>): Promise<SupplierProduct> => {\r\n    try {\r\n      const response = await apiClient.put<SupplierProduct>(`/products/${productId}`, productData);\r\n      if (!response.data) {\r\n        throw new Error(`Failed to update product ${productId}`);\r\n      }\r\n      return response.data;\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Upload product images\r\n   */\r\n  uploadProductImages: async (productId: string, files: File[]): Promise<{ imageUrls: string[] }> => {\r\n    try {\r\n      const formData = new FormData();\r\n      files.forEach((file, index) => {\r\n        formData.append(`images[${index}]`, file);\r\n      });\r\n\r\n      const response = await apiClient.post<{ imageUrls: string[] }>(`/products/${productId}/upload-images`, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n\r\n      if (!response.data) {\r\n        throw new Error('Failed to upload product images');\r\n      }\r\n      return response.data;\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get supplier documents\r\n   * TEMPORARY: Returns empty array if endpoint returns 404 (under development)\r\n   */\r\n  getSupplierDocuments: async (supplierId: string, params?: Record<string, any>): Promise<SupplierDocument[]> => {\r\n    try {\r\n      const response = await apiClient.get<SupplierDocument[]>(`/suppliers/${supplierId}/documents`, { params });\r\n      if (!response.data) {\r\n        return []; // Return empty array instead of throwing error\r\n      }\r\n      return response.data;\r\n    } catch (error: any) {\r\n      // Gracefully handle 404 errors for endpoints under development\r\n      if (error.response?.status === 404 || error.status === 404) {\r\n        console.warn(`[TEMP] Documents endpoint not yet implemented for supplier ${supplierId}`);\r\n        return []; // Return empty array for 404s\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get supplier analytics\r\n   * TEMPORARY: Returns null if endpoint returns 404 (under development)\r\n   */\r\n  getSupplierAnalytics: async (supplierId: string, params?: Record<string, any>): Promise<SupplierAnalyticsData | null> => {\r\n    try {\r\n      const response = await apiClient.get<SupplierAnalyticsData>(`/suppliers/${supplierId}/analytics`, { params });\r\n      if (!response.data) {\r\n        return null; // Return null instead of throwing error\r\n      }\r\n      return response.data;\r\n    } catch (error: any) {\r\n      // Gracefully handle 404 errors for endpoints under development\r\n      if (error.response?.status === 404 || error.status === 404) {\r\n        console.warn(`[TEMP] Analytics endpoint not yet implemented for supplier ${supplierId}`);\r\n        return null; // Return null for 404s\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Upload supplier logo/image\r\n   */\r\n  uploadSupplierImage: async (supplierId: string, file: File): Promise<{ imageUrl: string }> => {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('image', file);\r\n\r\n      const response = await apiClient.post<{ imageUrl: string }>(`/suppliers/${supplierId}/upload-image`, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n\r\n      if (!response.data) {\r\n        throw new Error('Failed to upload supplier image');\r\n      }\r\n      return response.data;\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Ban a supplier\r\n   */\r\n  banSupplier: async (id: string): Promise<Supplier> => {\r\n    try {\r\n      const response = await apiClient.put<ApiResponseWrapper<BackendSupplier>>(`/suppliers/${id}/ban`, { status: 'banned' });\r\n\r\n      // Handle wrapped response format\r\n      if (response.data && 'success' in response.data && response.data.success) {\r\n        return transformBackendSupplier(response.data.data);\r\n      } else {\r\n        // Fallback for non-wrapped responses (legacy support)\r\n        const supplier = response.data as unknown as BackendSupplier;\r\n        return transformBackendSupplier(supplier);\r\n      }\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Unban a supplier (reactivate)\r\n   */\r\n  unbanSupplier: async (id: string): Promise<Supplier> => {\r\n    try {\r\n      const response = await apiClient.put<ApiResponseWrapper<BackendSupplier>>(`/suppliers/${id}/unban`);\r\n\r\n      // Handle wrapped response format\r\n      if (response.data && 'success' in response.data && response.data.success) {\r\n        return transformBackendSupplier(response.data.data);\r\n      } else {\r\n        // Fallback for non-wrapped responses (legacy support)\r\n        const supplier = response.data as unknown as BackendSupplier;\r\n        return transformBackendSupplier(supplier);\r\n      }\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  }\r\n};\r\n\r\nexport default suppliersApi;\r\n", "/**\r\n * Suppliers Hook\r\n *\r\n * This hook provides methods and state for working with suppliers.\r\n */\r\n\r\nimport { useState, useCallback, useEffect, useRef } from 'react';\r\nimport type{ Supplier, SupplierFormData, SupplierProduct } from '../types/index';\r\nimport suppliersApi from '../api/suppliersApi';\r\nimport useNotification from '../../../hooks/useNotification';\r\n\r\nexport const useSuppliers = () => {\r\n  const [suppliers, setSuppliers] = useState<Supplier[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<Error | null>(null);\r\n  const { showNotification } = useNotification();\r\n\r\n  // Use ref to avoid dependency issues with showNotification\r\n  const showNotificationRef = useRef(showNotification);\r\n  const hasInitialFetched = useRef(false);\r\n\r\n  // Update ref when showNotification changes\r\n  useEffect(() => {\r\n    showNotificationRef.current = showNotification;\r\n  });\r\n\r\n  // Fetch all suppliers\r\n  const fetchSuppliers = useCallback(async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await suppliersApi.getSuppliers();\r\n      setSuppliers(data);\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to fetch suppliers'\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Create a new supplier\r\n  const createSupplier = useCallback(async (supplierData: SupplierFormData, showNotifications: boolean = true) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const newSupplier = await suppliersApi.createSupplier(supplierData);\r\n      setSuppliers(prevSuppliers => [...prevSuppliers, newSupplier]);\r\n      if (showNotifications) {\r\n        showNotificationRef.current({\r\n          type: 'success',\r\n          title: 'Success',\r\n          message: 'Supplier created successfully'\r\n        });\r\n      }\r\n      return newSupplier;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      // Don't show error notifications when showNotifications is false\r\n      // Let the form error handler manage the error display\r\n      if (showNotifications) {\r\n        showNotificationRef.current({\r\n          type: 'error',\r\n          title: 'Error',\r\n          message: 'Failed to create supplier'\r\n        });\r\n      }\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Update a supplier\r\n  const updateSupplier = useCallback(async (id: string, supplierData: Partial<SupplierFormData>) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const updatedSupplier = await suppliersApi.updateSupplier(id, supplierData);\r\n      setSuppliers(prevSuppliers =>\r\n        prevSuppliers.map(supplier => supplier.id === id ? updatedSupplier : supplier)\r\n      );\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: 'Supplier updated successfully'\r\n      });\r\n      return updatedSupplier;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to update supplier'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Delete a supplier\r\n  const deleteSupplier = useCallback(async (id: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      await suppliersApi.deleteSupplier(id);\r\n      setSuppliers(prevSuppliers => prevSuppliers.filter(supplier => supplier.id !== id));\r\n      // Note: Success notification is handled by the calling component for better UX\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to delete supplier'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Get supplier by ID\r\n  const getSupplierById = useCallback(async (id: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const supplier = await suppliersApi.getSupplierById(id);\r\n      return supplier;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to fetch supplier details'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Update supplier verification status (backend only supports verified/pending)\r\n  const updateVerificationStatus = useCallback(async (id: string, status: 'verified' | 'pending') => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const updatedSupplier = await suppliersApi.updateVerificationStatus(id, status);\r\n      setSuppliers(prevSuppliers =>\r\n        prevSuppliers.map(supplier => supplier.id === id ? updatedSupplier : supplier)\r\n      );\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: `Supplier ${status === 'verified' ? 'verified' : 'set to pending'} successfully`\r\n      });\r\n      return updatedSupplier;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to update supplier verification status`\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Load suppliers on mount\r\n  useEffect(() => {\r\n    if (!hasInitialFetched.current) {\r\n      hasInitialFetched.current = true;\r\n      fetchSuppliers();\r\n    }\r\n  }, []);\r\n\r\n  // Get supplier products\r\n  const getSupplierProducts = useCallback(async (supplierId: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const products = await suppliersApi.getSupplierProducts(supplierId);\r\n      return products;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to fetch supplier products'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Get product by ID\r\n  const getProductById = useCallback(async (productId: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const product = await suppliersApi.getProductById(productId);\r\n      return product;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to fetch product details'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Update product\r\n  const updateProduct = useCallback(async (productId: string, productData: Partial<SupplierProduct>) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const updatedProduct = await suppliersApi.updateProduct(productId, productData);\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: 'Product updated successfully'\r\n      });\r\n      return updatedProduct;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to update product'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Upload product images\r\n  const uploadProductImages = useCallback(async (productId: string, files: File[]) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const result = await suppliersApi.uploadProductImages(productId, files);\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: 'Product images uploaded successfully'\r\n      });\r\n      return result;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to upload product images'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Get supplier documents (gracefully handles 404s for endpoints under development)\r\n  const getSupplierDocuments = useCallback(async (supplierId: string) => {\r\n    try {\r\n      const documents = await suppliersApi.getSupplierDocuments(supplierId);\r\n      return documents;\r\n    } catch (err) {\r\n      // Only show error notifications for non-404 errors\r\n      const error = err as any;\r\n      if (error.response?.status !== 404 && error.status !== 404) {\r\n        setError(err as Error);\r\n        showNotificationRef.current({\r\n          type: 'error',\r\n          title: 'Error',\r\n          message: 'Failed to fetch supplier documents'\r\n        });\r\n      }\r\n      // Return empty array for any error to prevent breaking the UI\r\n      return [];\r\n    }\r\n  }, []);\r\n\r\n  // Get supplier analytics (gracefully handles 404s for endpoints under development)\r\n  const getSupplierAnalytics = useCallback(async (supplierId: string) => {\r\n    try {\r\n      const analytics = await suppliersApi.getSupplierAnalytics(supplierId);\r\n      return analytics;\r\n    } catch (err) {\r\n      // Only show error notifications for non-404 errors\r\n      const error = err as any;\r\n      if (error.response?.status !== 404 && error.status !== 404) {\r\n        setError(err as Error);\r\n        showNotificationRef.current({\r\n          type: 'error',\r\n          title: 'Error',\r\n          message: 'Failed to fetch supplier analytics'\r\n        });\r\n      }\r\n      // Return null for any error to prevent breaking the UI\r\n      return null;\r\n    }\r\n  }, []);\r\n\r\n  // Upload supplier image\r\n  const uploadSupplierImage = useCallback(async (supplierId: string, file: File) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const result = await suppliersApi.uploadSupplierImage(supplierId, file);\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: 'Supplier image uploaded successfully'\r\n      });\r\n      return result;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to upload supplier image'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Ban a supplier\r\n  const banSupplier = useCallback(async (id: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const bannedSupplier = await suppliersApi.banSupplier(id);\r\n      setSuppliers(prevSuppliers =>\r\n        prevSuppliers.map(supplier => supplier.id === id ? bannedSupplier : supplier)\r\n      );\r\n      // Note: Success notification is handled by the calling component for better UX\r\n      return bannedSupplier;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to ban supplier'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Unban a supplier\r\n  const unbanSupplier = useCallback(async (id: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const unbannedSupplier = await suppliersApi.unbanSupplier(id);\r\n      setSuppliers(prevSuppliers =>\r\n        prevSuppliers.map(supplier => supplier.id === id ? unbannedSupplier : supplier)\r\n      );\r\n      // Note: Success notification is handled by the calling component for better UX\r\n      return unbannedSupplier;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to unban supplier'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  return {\r\n    suppliers,\r\n    isLoading,\r\n    error,\r\n    fetchSuppliers,\r\n    getSupplierById,\r\n    createSupplier,\r\n    createEntity: createSupplier, // Alias for consistency with user pattern\r\n    updateSupplier,\r\n    deleteSupplier,\r\n    deleteEntity: deleteSupplier, // Alias for consistency with user pattern\r\n    updateVerificationStatus,\r\n    getSupplierProducts,\r\n    getSupplierDocuments,\r\n    getSupplierAnalytics,\r\n    uploadSupplierImage,\r\n    banSupplier,\r\n    unbanSupplier,\r\n    // Product operations\r\n    getProductById,\r\n    updateProduct,\r\n    uploadProductImages\r\n  };\r\n};\r\n\r\nexport default useSuppliers;\r\n"], "names": ["ClockIcon", "_ref", "svgRef", "title", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "XCircleIcon", "transformBackendSupplier", "backendSupplier", "name", "email", "phone", "address", "status", "verificationStatus", "categories", "<PERSON><PERSON><PERSON>", "logo", "image", "website", "transformBackendSupplierProduct", "backendProduct", "sku", "category", "price", "stock", "minimumStock", "description", "images", "attributes", "variants", "createdAt", "createdDate", "Date", "toISOString", "updatedAt", "updatedDate", "getSuppliers", "async", "response", "apiClient", "get", "params", "data", "success", "wrappedResponse", "suppliers", "map", "Array", "isArray", "error", "handleApiError", "getSupplierById", "supplier", "createSupplier", "apiData", "supplierData", "password", "supplierName", "businessType", "post", "updateSupplier", "put", "responseValidators", "update", "deleteSupplier", "delete", "updateVerificationStatus", "getSuppliersByVerificationStatus", "Error", "getSupplierProducts", "supplierId", "products", "getProductById", "productId", "updateProduct", "productData", "uploadProductImages", "files", "formData", "FormData", "for<PERSON>ach", "file", "index", "append", "headers", "getSupplierDocuments", "_error$response", "console", "warn", "getSupplierAnalytics", "_error$response2", "uploadSupplierImage", "banSupplier", "unbanSupplier", "useSuppliers", "setSuppliers", "useState", "isLoading", "setIsLoading", "setError", "showNotification", "useNotification", "showNotificationRef", "useRef", "hasInitialFetched", "useEffect", "current", "fetchSuppliers", "useCallback", "suppliersApi", "err", "type", "message", "showNotifications", "arguments", "length", "undefined", "newSupplier", "prevSuppliers", "updatedSupplier", "filter", "updatedProduct", "result", "bannedSupplier", "unbannedSupplier", "createEntity", "deleteEntity"], "sourceRoot": ""}