"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[584],{312:(e,t,s)=>{s.d(t,{A:()=>r});s(5043);var a=s(579);const r=e=>{let{children:t,className:s=""}=e;return(0,a.jsx)("dl",{className:`sm:divide-y sm:divide-gray-200 ${s}`,children:t})}},2659:(e,t,s)=>{s.d(t,{A:()=>r});s(5043);var a=s(579);const r=e=>{let{tabs:t,activeTab:s,onChange:r,className:l=""}=e;return(0,a.jsx)("div",{className:`border-b border-gray-200 ${l}`,children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:t.map((e=>{const t=e.id===s;return(0,a.jsx)("button",{onClick:()=>!e.disabled&&r(e.id),className:`\n                whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm\n                focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2\n                ${t?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}\n                ${e.disabled?"opacity-50 cursor-not-allowed":"cursor-pointer"}\n              `,disabled:e.disabled,children:e.label},e.id)}))})})}},2671:(e,t,s)=>{s.d(t,{A:()=>c});var a=s(5043),r=s(3593),l=s(579);const n=e=>{if(!a.isValidElement(e))return"bg-primary bg-opacity-10";const t=(e.props.className||"").match(/text-([a-z]+)-/);if(t){return`bg-${t[1]}-50`}return"bg-primary bg-opacity-10"},i=e=>{let{title:t,data:s,icon:i,formatValue:c=e=>e.toString()}=e;return(0,l.jsx)(r.A,{children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:`p-3 rounded-full ${n(i)}`,children:a.isValidElement(i)?(()=>{const e=i,t=(e.props.className||"").match(/text-[a-z0-9-]+/),s=t?t[0]:"text-gray-600";return a.cloneElement(e,{className:`h-6 w-6 ${s}`})})():i}),(0,l.jsxs)("div",{className:"ml-4 flex-1",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-500",children:t}),(0,l.jsxs)("div",{className:"flex items-baseline",children:[(0,l.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:c(s.total)}),void 0!==s.growth&&(0,l.jsxs)("p",{className:"ml-2 flex items-baseline text-sm font-semibold "+(s.growth>=0?"text-green-600":"text-red-600"),children:[s.growth>=0?"+":"",s.growth.toFixed(1),"%"]})]})]})]})})},c=e=>{let{metrics:t,className:s=""}=e;return(0,l.jsx)("div",{className:`grid grid-cols-1 md:grid-cols-3 gap-6 ${s}`,children:t.map(((e,t)=>(0,l.jsx)(i,{title:e.title,data:{total:"string"===typeof e.value?parseFloat(e.value)||0:e.value,growth:e.change||0},icon:e.icon},t)))})}},2683:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(5043);function r(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const l=a.forwardRef(r)},2751:(e,t,s)=>{s.r(t),s.d(t,{default:()=>te});var a=s(5043),r=s(3216),l=s(2806),n=s(2659),i=s(3927),c=s(7907),o=s(8100),d=s(9705),m=s(4538);function x(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18.364 18.364A9 9 0 0 0 5.636 5.636m12.728 12.728A9 9 0 0 1 5.636 5.636m12.728 12.728L5.636 5.636"}))}const u=a.forwardRef(x);var h=s(9248),p=s(6887),f=s(312),g=s(5149),v=s(4692),j=s(8300),y=s(7012),b=s(7098),w=s(4129),N=s(8682),k=s(9850);function A(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))}const C=a.forwardRef(A);function E(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))}const S=a.forwardRef(E);var L=s(579);const B=e=>{let{supplier:t}=e;return(0,L.jsxs)("div",{className:"space-y-6",children:[(0,L.jsx)(p.A,{title:"Supplier Overview",description:"Basic supplier information and verification status",children:(0,L.jsx)("div",{className:"px-6 py-4",children:(0,L.jsx)("div",{className:"flex items-center justify-between",children:(0,L.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,L.jsx)(j.A,{...t.logo&&{src:t.logo},alt:t.name,name:t.name,size:"xl"}),(0,L.jsxs)("div",{children:[(0,L.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:t.name}),(0,L.jsxs)("p",{className:"text-sm text-gray-500",children:["ID: ",t.id]}),(0,L.jsxs)("div",{className:"mt-2 flex items-center space-x-3",children:[(0,L.jsx)(v.A,{status:t.status,type:"supplier"}),(0,L.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("verified"===t.verificationStatus?"bg-green-100 text-green-800":"pending"===t.verificationStatus?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:t.verificationStatus?t.verificationStatus.charAt(0).toUpperCase()+t.verificationStatus.slice(1):"Unknown"})]})]})]})})})}),(0,L.jsx)(p.A,{title:"Contact Information",description:"Primary contact details and communication preferences",children:(0,L.jsxs)(f.A,{children:[(0,L.jsx)(g.A,{label:"Contact Person",value:(0,L.jsxs)("div",{className:"flex items-center",children:[(0,L.jsx)(w.A,{className:"w-4 h-4 text-gray-400 mr-2"}),t.contactPerson]})}),(0,L.jsx)(g.A,{label:"Email Address",value:(0,L.jsxs)("div",{className:"flex items-center",children:[(0,L.jsx)(N.A,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,L.jsx)("a",{href:`mailto:${t.email}`,className:"text-primary hover:text-primary-dark",children:t.email})]})}),(0,L.jsx)(g.A,{label:"Phone Number",value:(0,L.jsxs)("div",{className:"flex items-center",children:[(0,L.jsx)(k.A,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,L.jsx)("a",{href:`tel:${t.phone}`,className:"text-primary hover:text-primary-dark",children:t.phone})]})}),(0,L.jsx)(g.A,{label:"Address",value:(0,L.jsxs)("div",{className:"flex items-start",children:[(0,L.jsx)(C,{className:"w-4 h-4 text-gray-400 mr-2 mt-0.5"}),(0,L.jsx)("span",{children:t.address})]})}),t.website&&(0,L.jsx)(g.A,{label:"Website",value:(0,L.jsxs)("div",{className:"flex items-center",children:[(0,L.jsx)(S,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,L.jsx)("a",{href:t.website,target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:text-primary-dark",children:t.website})]})})]})}),(0,L.jsx)(p.A,{title:"Business Information",description:"Business categories and operational details",children:(0,L.jsxs)(f.A,{children:[(0,L.jsx)(g.A,{label:"Business Categories",value:(0,L.jsx)("div",{className:"flex flex-wrap gap-2",children:t.categories&&t.categories.length>0?t.categories.map(((e,t)=>(0,L.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e},t))):(0,L.jsx)("span",{className:"text-gray-500 text-sm",children:"No categories assigned"})})}),(0,L.jsx)(g.A,{label:"Account Status",value:(0,L.jsx)(v.A,{status:t.status,type:"supplier"})})]})}),(0,L.jsx)(p.A,{title:"Verification Status",description:"Current verification status and history",children:(0,L.jsx)("div",{className:"px-6 py-4",children:(0,L.jsxs)("div",{className:"flex items-center space-x-3",children:[(e=>{switch(e){case"verified":return(0,L.jsx)(m.A,{className:"w-5 h-5 text-green-500"});case"pending":return(0,L.jsx)(y.A,{className:"w-5 h-5 text-yellow-500"});case"rejected":return(0,L.jsx)(b.A,{className:"w-5 h-5 text-red-500"});default:return(0,L.jsx)(y.A,{className:"w-5 h-5 text-gray-500"})}})(t.verificationStatus||"pending"),(0,L.jsxs)("div",{children:[(0,L.jsx)("div",{className:"text-sm font-medium text-gray-900",children:"verified"===t.verificationStatus?"Verified Supplier":"pending"===t.verificationStatus?"Verification Pending":"Verification Rejected"}),(0,L.jsx)("div",{className:"text-sm text-gray-500",children:(e=>{switch(e){case"verified":return"Verified";case"pending":return"Pending verification";case"rejected":return"Rejected";default:return"Unknown status"}})(t.verificationStatus||"pending")})]})]})})})]})};function D(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const I=a.forwardRef(D);function P(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const $=a.forwardRef(P);var M=s(7988),R=s(9531),F=s(4791),O=s(3893);const z=e=>{let{documents:t,supplierId:s}=e;const{showInfo:r}=(0,d.A)(),[l,n]=(0,a.useState)(null),[i,m]=(0,a.useState)(!1),x=e=>{r(`Downloading ${e.name}...`),console.log("Download document:",e)};return 0===t.length?(0,L.jsx)(p.A,{title:"Verification Documents",description:"Documents submitted for supplier verification",children:(0,L.jsxs)("div",{className:"px-6 py-8 text-center",children:[(0,L.jsx)(I,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,L.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No documents uploaded"}),(0,L.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"This supplier has not uploaded any verification documents yet."})]})}):(0,L.jsxs)("div",{className:"space-y-6",children:[(0,L.jsx)(p.A,{title:"Verification Documents",description:"Documents submitted for supplier verification and compliance",children:(0,L.jsx)("div",{className:"overflow-x-auto",children:(0,L.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,L.jsx)("thead",{className:"bg-gray-50",children:(0,L.jsxs)("tr",{children:[(0,L.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Document"}),(0,L.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,L.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Upload Date"}),(0,L.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,L.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map((e=>{return(0,L.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,L.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,L.jsxs)("div",{className:"flex items-center",children:[(0,L.jsx)($,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,L.jsxs)("div",{children:[(0,L.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,L.jsxs)("div",{className:"text-sm text-gray-500",children:[e.fileName," \u2022 ",(0,O.v7)(e.fileSize)]})]})]})}),(0,L.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,L.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:(t=e.type,{business_license:"Business License",tax_certificate:"Tax Certificate",insurance:"Insurance Policy",certification:"Certification",other:"Other Document"}[t]||"Unknown Document")})}),(0,L.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,L.jsxs)("div",{className:"flex items-center text-sm text-gray-900",children:[(0,L.jsx)(M.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,O.Yq)(e.uploadDate)]})}),(0,L.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,L.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,L.jsx)(c.A,{variant:"outline",size:"xs",onClick:()=>(e=>{n(e),m(!0)})(e),icon:(0,L.jsx)(R.A,{className:"w-4 h-4"}),children:"View"}),(0,L.jsx)(c.A,{variant:"outline",size:"xs",onClick:()=>x(e),icon:(0,L.jsx)(F.A,{className:"w-4 h-4"}),children:"Download"})]})})]},e.id);var t}))})]})})}),l&&(0,L.jsx)(o.A,{isOpen:i,onClose:()=>m(!1),title:`Document Preview: ${l.name}`,size:"lg",footer:(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(c.A,{variant:"outline",onClick:()=>m(!1),children:"Close"}),(0,L.jsx)(c.A,{variant:"primary",onClick:()=>x(l),icon:(0,L.jsx)(F.A,{className:"w-4 h-4"}),children:"Download"})]}),children:(0,L.jsxs)("div",{className:"space-y-4",children:[(0,L.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,L.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,L.jsxs)("div",{children:[(0,L.jsx)("span",{className:"font-medium text-gray-500",children:"File Name:"}),(0,L.jsx)("div",{className:"text-gray-900",children:l.fileName})]}),(0,L.jsxs)("div",{children:[(0,L.jsx)("span",{className:"font-medium text-gray-500",children:"File Size:"}),(0,L.jsx)("div",{className:"text-gray-900",children:(0,O.v7)(l.fileSize)})]}),(0,L.jsxs)("div",{children:[(0,L.jsx)("span",{className:"font-medium text-gray-500",children:"Upload Date:"}),(0,L.jsx)("div",{className:"text-gray-900",children:(0,O.Yq)(l.uploadDate)})]})]}),l.notes&&(0,L.jsxs)("div",{className:"mt-4",children:[(0,L.jsx)("span",{className:"font-medium text-gray-500",children:"Notes:"}),(0,L.jsx)("div",{className:"text-gray-900 mt-1",children:l.notes})]})]}),(0,L.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center",children:[(0,L.jsx)($,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,L.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Document preview not available. Click download to view the file."})]})]})})]})};var V=s(7422),T=s(724),W=s(8267),U=s(2811),H=s(8662);const Z=e=>{let{products:t,supplierId:s,onProductUpdate:l}=e;const n=(0,r.Zp)(),{showSuccess:i,showError:m,showInfo:x}=(0,d.A)(),[u,f]=(0,a.useState)(null),[g,j]=(0,a.useState)(!1),y=e=>{switch(e){case"active":return"active";case"inactive":default:return"pending";case"out_of_stock":return"rejected"}},b=e=>{n(T.b.getProductDetailsRoute(e.id))},w=[{key:"name",label:"Product Name",sortable:!0,render:(e,t)=>(0,L.jsxs)("div",{className:"flex items-center",children:[t.image?(0,L.jsx)("img",{src:t.image,alt:t.name,className:"h-10 w-10 rounded-lg object-cover mr-3"}):(0,L.jsx)("div",{className:"h-10 w-10 bg-gray-200 rounded-lg flex items-center justify-center mr-3",children:(0,L.jsx)(W.A,{className:"h-5 w-5 text-gray-400"})}),(0,L.jsxs)("div",{children:[(0,L.jsx)("div",{className:"font-medium text-gray-900",children:t.name}),(0,L.jsxs)("div",{className:"text-xs text-gray-500",children:["ID: ",t.id]})]})]})},{key:"sku",label:"SKU",sortable:!0,render:e=>(0,L.jsx)("span",{className:"font-mono text-sm text-gray-600",children:e})},{key:"category",label:"Category",sortable:!0,render:e=>(0,L.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e})},{key:"price",label:"Price",sortable:!0,render:e=>(0,L.jsx)("span",{className:"font-medium text-gray-900",children:(0,O.vv)(e)})},{key:"stock",label:"Stock",sortable:!0,render:(e,t)=>{const s=(a=e,r=t.minimumStock||10,"out_of_stock"===t.status||0===a?{text:"Out of Stock",color:"text-red-600"}:a<=r?{text:"Low Stock",color:"text-yellow-600"}:{text:"In Stock",color:"text-green-600"});var a,r;return(0,L.jsxs)("div",{children:[(0,L.jsx)("div",{className:"font-medium text-gray-900",children:e}),(0,L.jsx)("div",{className:`text-xs ${s.color}`,children:s.text})]})}},{key:"status",label:"Status",sortable:!0,render:e=>(0,L.jsx)(v.A,{status:y(e),type:"supplier"})},{key:"actions",label:"Actions",sortable:!1,render:(e,t)=>(0,L.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,L.jsx)(c.A,{variant:"outline",size:"xs",onClick:()=>b(t),icon:(0,L.jsx)(R.A,{className:"w-4 h-4"}),title:"View product details",children:"View"}),(0,L.jsx)(c.A,{variant:"outline",size:"xs",onClick:()=>(e=>{x(`Edit product: ${e.name}`),console.log("Edit product:",e)})(t),icon:(0,L.jsx)(U.A,{className:"w-4 h-4"}),title:"Edit product",children:"Edit"}),(0,L.jsx)(c.A,{variant:"danger",size:"xs",onClick:()=>(e=>{f(e),j(!0)})(t),icon:(0,L.jsx)(h.A,{className:"w-4 h-4"}),title:"Delete product",children:"Delete"})]})}];return 0===t.length?(0,L.jsx)(p.A,{title:"Products",description:"Products offered by this supplier",children:(0,L.jsxs)("div",{className:"px-6 py-8 text-center",children:[(0,L.jsx)(W.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,L.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No products found"}),(0,L.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"This supplier has not added any products yet."}),(0,L.jsx)("div",{className:"mt-6",children:(0,L.jsx)(c.A,{variant:"primary",icon:(0,L.jsx)(H.A,{className:"w-4 h-4"}),onClick:()=>x("Add product functionality coming soon"),children:"Add Product"})})]})}):(0,L.jsxs)("div",{className:"space-y-6",children:[(0,L.jsx)(p.A,{title:"Products",description:`${t.length} products offered by this supplier`,children:(0,L.jsx)(V.A,{columns:w,data:t,onRowClick:b,pagination:!0,pageSize:10,emptyMessage:"No products found",className:"border-0"})}),u&&(0,L.jsx)(o.A,{isOpen:g,onClose:()=>j(!1),title:"Delete Product",size:"sm",footer:(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(c.A,{variant:"outline",onClick:()=>j(!1),children:"Cancel"}),(0,L.jsx)(c.A,{variant:"danger",onClick:async()=>{if(u)try{i(`Product "${u.name}" deleted successfully`),j(!1),f(null),null===l||void 0===l||l()}catch(e){m("Failed to delete product")}},children:"Delete Product"})]}),children:(0,L.jsxs)("div",{className:"text-sm text-gray-500",children:['Are you sure you want to delete "',u.name,'"? This action cannot be undone.']})})]})};var _=s(3488),q=s(2683),K=s(2671),Y=s(3593);const G=e=>{let{title:t,description:s,children:a,className:r=""}=e;return(0,L.jsxs)(Y.A,{className:r,children:[(0,L.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,L.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:t}),s&&(0,L.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:s})]}),(0,L.jsx)("div",{className:"px-4 py-5 sm:p-6",children:a})]})},J=e=>{let{title:t,labels:s,data:a,color:r="#F28B22"}=e;const l=Math.max(...a);return(0,L.jsx)(Y.A,{title:t,children:(0,L.jsx)("div",{className:"h-80 flex items-center justify-center",children:(0,L.jsx)("div",{className:"w-full h-full",children:(0,L.jsxs)("div",{className:"w-full h-full flex flex-col",children:[(0,L.jsx)("div",{className:"flex justify-between mb-4",children:s.map(((e,t)=>(0,L.jsx)("div",{className:"text-xs text-gray-500",children:e},t)))}),(0,L.jsx)("div",{className:"flex-1 flex items-end",children:a.map(((e,t)=>{const s=l>0?e/l*100+"%":"0%";return(0,L.jsx)("div",{className:"flex-1 mx-1",children:(0,L.jsx)("div",{className:"rounded-t-md w-full transition-all duration-500",style:{height:s,backgroundColor:r}})},t)}))})]})})})})},Q=e=>{let{title:t,data:s}=e;const a=s.datasets[0];if(!a||!a.data||0===a.data.length)return(0,L.jsx)(Y.A,{title:t,children:(0,L.jsx)("div",{className:"h-80 flex items-center justify-center",children:(0,L.jsx)("div",{className:"text-center text-gray-500",children:"No data available"})})});const r=a.data.reduce(((e,t)=>e+t),0);return(0,L.jsx)(Y.A,{title:t,children:(0,L.jsx)("div",{className:"h-80 flex items-center justify-center",children:(0,L.jsxs)("div",{className:"w-full h-full flex flex-col",children:[(0,L.jsx)("div",{className:"mb-4",children:s.labels.map(((e,t)=>{var s;return(0,L.jsxs)("div",{className:"flex items-center mb-2",children:[(0,L.jsx)("div",{className:"w-4 h-4 rounded mr-2",style:{backgroundColor:(null===(s=a.backgroundColor)||void 0===s?void 0:s[t])||"#ccc"}}),(0,L.jsxs)("span",{className:"text-sm text-gray-600",children:[e,": ",a.data[t]||0," (",((a.data[t]||0)/r*100).toFixed(1),"%)"]})]},t)}))}),(0,L.jsx)("div",{className:"flex-1 flex items-center justify-center",children:(0,L.jsxs)("div",{className:"text-center",children:[(0,L.jsxs)("div",{className:"text-2xl font-bold text-gray-900 mb-2",children:["Total: ",r]}),(0,L.jsx)("div",{className:"text-sm text-gray-500",children:"Data Distribution"})]})})]})})})},X=e=>{let{supplierData:t,supplierId:s}=e;const a=[{title:"Total Orders",value:t.totalOrders,icon:(0,L.jsx)(_.A,{className:"w-6 h-6 text-blue-500"})},{title:"Total Revenue",value:(0,O.vv)(t.totalRevenue),icon:(0,L.jsx)(q.A,{className:"w-6 h-6 text-green-500"})},{title:"Products",value:t.productCount,icon:(0,L.jsx)(W.A,{className:"w-6 h-6 text-purple-500"})}],r=t.revenueHistory.map((e=>({label:e.date,value:e.amount}))),l=t.salesByProduct.map((e=>({label:e.productName,value:e.amount}))),n=t.orderTrends.map((e=>({label:e.date,value:e.orders})));return(0,L.jsxs)("div",{className:"space-y-6",children:[(0,L.jsx)(K.A,{metrics:a}),(0,L.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,L.jsx)(G,{title:"Revenue History",description:"Supplier's revenue over time",children:(0,L.jsx)(J,{data:r.map((e=>e.value)),labels:r.map((e=>e.label)),title:"Revenue History",color:"#F28B22"})}),(0,L.jsx)(G,{title:"Order Trends",description:"Number of orders over time",children:(0,L.jsx)(J,{data:n.map((e=>e.value)),labels:n.map((e=>e.label)),title:"Order Trends",color:"#3B82F6"})})]}),(0,L.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,L.jsx)(G,{title:"Sales by Product",description:"Revenue distribution by product",children:(0,L.jsx)(Q,{title:"Sales by Product",data:{labels:l.map((e=>e.label)),datasets:[{data:l.map((e=>e.value)),backgroundColor:["#F28B22","#F9B16F","#D17311","#FFC380","#A85A0D"]}]}})}),(0,L.jsx)(G,{title:"Top Categories",description:"Revenue by product category",children:(0,L.jsx)(Q,{title:"Top Categories",data:{labels:t.topCategories.map((e=>e.category)),datasets:[{data:t.topCategories.map((e=>e.revenue)),backgroundColor:["#10B981","#3B82F6","#8B5CF6","#F59E0B","#EF4444"]}]}})})]})]})};var ee=s(8736);const te=()=>{const{id:e}=(0,r.g)(),t=(0,r.Zp)(),{showError:s,showSuccess:x}=(0,d.A)(),p=(0,a.useRef)(s),f=(0,a.useRef)(x),g=(0,a.useRef)(!0);(0,a.useEffect)((()=>{p.current=s,f.current=x}),[s,x]),(0,a.useEffect)((()=>()=>{g.current=!1}),[]);const[v,j]=(0,a.useState)("personal"),[y,b]=(0,a.useState)(null),[w,N]=(0,a.useState)([]),[k,A]=(0,a.useState)([]),[C,E]=(0,a.useState)(null),[S,D]=(0,a.useState)(!0),[I,P]=(0,a.useState)(null),[$,M]=(0,a.useState)(!1),[R,F]=(0,a.useState)(!1),[O,V]=(0,a.useState)(!1),[W,U]=(0,a.useState)(!1),[H,_]=(0,a.useState)(!0),[q,K]=(0,a.useState)(!0),{getSupplierById:Y,getSupplierProducts:G,getSupplierDocuments:J,getSupplierAnalytics:Q,deleteSupplier:te,banSupplier:se,unbanSupplier:ae,isLoading:re}=(0,ee.h)(),le=(0,a.useCallback)((async()=>{if(!e)return P("No supplier ID provided"),void D(!1);try{D(!0),P(null);const t=await Y(e);if(!g.current)return;b(t);const s=await G(e);if(!g.current)return;N(s);const[a,r]=await Promise.all([J(e),Q(e)]);if(!g.current)return;A(a||[]),_(a&&a.length>0),E(r),K(null!==r)}catch(I){if(!g.current)return;console.error("Error fetching supplier data:",I);const t=I instanceof Error?I.message:"Failed to fetch supplier data";P(t),p.current("Failed to load supplier data")}finally{g.current&&D(!1)}}),[e,Y,G,J,Q]);(0,a.useEffect)((()=>{le()}),[le]),(0,a.useEffect)((()=>{("documents"!==v||H)&&("analytics"!==v||q)||j("personal")}),[v,H,q]);return S||re?(0,L.jsx)("div",{className:"flex justify-center items-center min-h-screen",children:(0,L.jsx)(i.A,{size:"lg"})}):I||!y?(0,L.jsxs)("div",{className:"space-y-6",children:[(0,L.jsx)(l.A,{title:"Supplier Profile",description:"Supplier not found",breadcrumbs:[{label:"Suppliers",path:T.b.SUPPLIERS},{label:"Profile"}]}),(0,L.jsx)("div",{className:"text-center py-12",children:(0,L.jsx)("p",{className:"text-gray-500",children:I||"Supplier not found"})})]}):(0,L.jsxs)("div",{className:"space-y-6",children:[(0,L.jsx)(l.A,{title:`Supplier: ${y.name}`,description:"Comprehensive supplier profile and management",breadcrumbs:[{label:"Suppliers",path:T.b.SUPPLIERS},{label:y.name}],actions:(0,L.jsxs)("div",{className:"flex gap-2",children:["banned"===y.status?(0,L.jsx)(c.A,{variant:"success",size:"sm",onClick:async()=>{if(y)try{U(!0),await ae(y.id),f.current(`Supplier "${y.name}" has been unbanned successfully`),await le()}catch(I){console.error("Error unbanning supplier:",I),p.current("Failed to unban supplier")}finally{U(!1)}},icon:(0,L.jsx)(m.A,{className:"h-4 w-4"}),disabled:S||W||R,loading:W,children:"Unban Supplier"}):(0,L.jsx)(c.A,{variant:"secondary",size:"sm",onClick:()=>V(!0),icon:(0,L.jsx)(u,{className:"h-4 w-4"}),disabled:S||W||R,children:"Ban Supplier"}),(0,L.jsx)(c.A,{variant:"danger",size:"sm",onClick:()=>M(!0),icon:(0,L.jsx)(h.A,{className:"h-4 w-4"}),disabled:S||R||W,children:"Delete Supplier"})]})}),(0,L.jsx)(n.A,{tabs:[{id:"personal",label:"Personal Information"},{id:"documents",label:H?"Verification Documents":"Documents (Coming Soon)",disabled:!H},{id:"products",label:"Products"},{id:"analytics",label:q?"Analytics":"Analytics (Coming Soon)",disabled:!q}],activeTab:v,onChange:e=>{("documents"!==e||H)&&("analytics"!==e||q)&&j(e)}}),"personal"===v&&(0,L.jsx)(B,{supplier:y}),"documents"===v&&(H?(0,L.jsx)(z,{documents:k,supplierId:y.id}):(0,L.jsxs)("div",{className:"bg-white rounded-lg shadow p-8 text-center",children:[(0,L.jsx)("div",{className:"text-gray-400 mb-4",children:(0,L.jsx)("svg",{className:"mx-auto h-12 w-12",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,L.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,L.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Documents Coming Soon"}),(0,L.jsx)("p",{className:"text-gray-500",children:"The verification documents feature is currently under development and will be available soon."})]})),"products"===v&&(0,L.jsx)(Z,{products:w,supplierId:y.id,onProductUpdate:le}),"analytics"===v&&(q&&C?(0,L.jsx)(X,{supplierData:C,supplierId:y.id}):(0,L.jsxs)("div",{className:"bg-white rounded-lg shadow p-8 text-center",children:[(0,L.jsx)("div",{className:"text-gray-400 mb-4",children:(0,L.jsx)("svg",{className:"mx-auto h-12 w-12",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,L.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),(0,L.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Analytics Coming Soon"}),(0,L.jsx)("p",{className:"text-gray-500",children:"The analytics dashboard is currently under development and will be available soon."})]})),(0,L.jsx)(o.A,{isOpen:O,onClose:()=>V(!1),title:"Ban Supplier",size:"sm",footer:(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(c.A,{variant:"outline",onClick:()=>V(!1),disabled:W,children:"Cancel"}),(0,L.jsx)(c.A,{variant:"secondary",onClick:async()=>{if(y)try{U(!0),await se(y.id),f.current(`Supplier "${y.name}" has been banned successfully`),V(!1),await le()}catch(I){console.error("Error banning supplier:",I),p.current("Failed to ban supplier")}finally{U(!1)}},loading:W,icon:(0,L.jsx)(u,{className:"h-4 w-4"}),children:"Ban Supplier"})]}),children:(0,L.jsxs)("div",{className:"text-sm text-gray-500",children:[(0,L.jsxs)("p",{className:"mb-3",children:["Are you sure you want to ban ",(0,L.jsxs)("strong",{children:['"',y.name,'"']}),"?"]}),(0,L.jsx)("p",{className:"text-orange-600 font-medium",children:"This action will:"}),(0,L.jsxs)("ul",{className:"mt-2 list-disc list-inside text-orange-600",children:[(0,L.jsx)("li",{children:"Change the supplier's status to 'banned'"}),(0,L.jsx)("li",{children:"Prevent them from receiving new orders"}),(0,L.jsx)("li",{children:"Restrict their access to the platform"}),(0,L.jsx)("li",{children:"Allow for potential future reactivation"})]}),(0,L.jsx)("p",{className:"mt-3 text-gray-600",children:"Unlike deletion, this action can be reversed by changing the supplier's status back to 'active'."})]})}),(0,L.jsx)(o.A,{isOpen:$,onClose:()=>M(!1),title:"Delete Supplier",size:"sm",footer:(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(c.A,{variant:"outline",onClick:()=>M(!1),disabled:R,children:"Cancel"}),(0,L.jsx)(c.A,{variant:"danger",onClick:async()=>{if(y)try{F(!0),await te(y.id),f.current(`Supplier "${y.name}" has been deleted successfully`),M(!1),t(T.b.SUPPLIERS)}catch(I){console.error("Error deleting supplier:",I),p.current("Failed to delete supplier")}finally{F(!1)}},loading:R,icon:(0,L.jsx)(h.A,{className:"h-4 w-4"}),children:"Delete Supplier"})]}),children:(0,L.jsxs)("div",{className:"text-sm text-gray-500",children:[(0,L.jsxs)("p",{className:"mb-3",children:["Are you sure you want to delete ",(0,L.jsxs)("strong",{children:['"',y.name,'"']}),"?"]}),(0,L.jsx)("p",{className:"text-red-600 font-medium",children:"This action cannot be undone and will permanently remove:"}),(0,L.jsxs)("ul",{className:"mt-2 list-disc list-inside text-red-600",children:[(0,L.jsx)("li",{children:"All supplier information"}),(0,L.jsx)("li",{children:"Associated products and documents"}),(0,L.jsx)("li",{children:"Order history and analytics"})]})]})})]})}},2806:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(5043),r=s(5475),l=s(5501),n=s(6365),i=s(579);const c=e=>{let{title:t,description:s,actions:a,breadcrumbs:c,className:o="",testId:d}=e;return(0,i.jsxs)("div",{className:`mb-6 ${o}`,"data-testid":d,children:[c&&c.length>0&&(0,i.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,i.jsxs)("ol",{className:"flex items-center space-x-1 text-sm text-gray-500",children:[(0,i.jsx)("li",{children:(0,i.jsx)(r.N_,{to:"/",className:"flex items-center hover:text-primary","aria-label":"Home",children:(0,i.jsx)(l.A,{className:"h-4 w-4"})})}),c.map(((e,t)=>(0,i.jsxs)("li",{className:"flex items-center",children:[(0,i.jsx)(n.A,{className:"h-4 w-4 mx-1 text-gray-400"}),e.path&&t<c.length-1?(0,i.jsx)(r.N_,{to:e.path,className:"hover:text-primary",children:e.label}):(0,i.jsx)("span",{className:"font-medium text-gray-700",children:e.label})]},t)))]})}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:t}),s&&"string"===typeof s?(0,i.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:s}):s]}),a&&(0,i.jsx)("div",{className:"flex flex-wrap gap-3 mt-2 sm:mt-0",children:a})]})]})},o=(0,a.memo)(c)},2811:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(5043);function r(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}const l=a.forwardRef(r)},3893:(e,t,s)=>{s.d(t,{Yq:()=>a,v7:()=>l,vv:()=>r});const a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return"-";try{const s=new Date(e),a={year:"numeric",month:"short",day:"numeric",...t};return new Intl.DateTimeFormat("en-US",a).format(s)}catch(s){return console.error("Error formatting date:",s),e}},r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en-US";try{return new Intl.NumberFormat(s,{style:"currency",currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}catch(a){return console.error("Error formatting currency:",a),`${t} ${e.toFixed(2)}`}},l=e=>{if(0===e)return"0 Bytes";const t=Math.floor(Math.log(e)/Math.log(1024));return`${parseFloat((e/Math.pow(1024,t)).toFixed(2))} ${["Bytes","KB","MB","GB","TB"][t]}`}},4129:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(5043);function r(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}const l=a.forwardRef(r)},4692:(e,t,s)=>{s.d(t,{A:()=>o});s(5043);var a=s(4538),r=s(7012),l=s(7098),n=s(5889),i=s(3867),c=s(579);const o=e=>{let{status:t,type:s="user",className:o=""}=e;if(!t)return(0,c.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${o}`,children:"Unknown"});const d=t.toLowerCase();let m="",x=null;"active"===d||"verified"===d||"completed"===d?(m="bg-green-100 text-green-800",x=(0,c.jsx)(a.A,{className:"w-4 h-4 mr-1"})):"pending"===d||"processing"===d?(m="bg-blue-100 text-blue-800",x=(0,c.jsx)(r.A,{className:"w-4 h-4 mr-1"})):"banned"===d||"rejected"===d?(m="bg-red-100 text-red-800",x=(0,c.jsx)(l.A,{className:"w-4 h-4 mr-1"})):"shipped"===d?(m="bg-purple-100 text-purple-800",x=(0,c.jsx)(n.A,{className:"w-4 h-4 mr-1"})):"warning"===d?(m="bg-yellow-100 text-yellow-800",x=(0,c.jsx)(i.A,{className:"w-4 h-4 mr-1"})):m="bg-gray-100 text-gray-800";const u=t?t.charAt(0).toUpperCase()+t.slice(1):"Unknown";return(0,c.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${m} ${o}`,children:[x,u]})}},4791:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(5043);function r(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"}))}const l=a.forwardRef(r)},5149:(e,t,s)=>{s.d(t,{A:()=>r});s(5043);var a=s(579);const r=e=>{let{label:t,value:s,className:r=""}=e;return(0,a.jsxs)("div",{className:`py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 ${r}`,children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:t}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:s})]})}},5889:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(5043);function r(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"}))}const l=a.forwardRef(r)},6365:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(5043);function r(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}const l=a.forwardRef(r)},6887:(e,t,s)=>{s.d(t,{A:()=>r});s(5043);var a=s(579);const r=e=>{let{title:t,description:s,children:r,className:l=""}=e;return(0,a.jsxs)("div",{className:`bg-white shadow overflow-hidden sm:rounded-lg ${l}`,children:[(0,a.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:t}),s&&(0,a.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:s})]}),(0,a.jsx)("div",{className:"border-t border-gray-200",children:r})]})}},7988:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(5043);function r(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))}const l=a.forwardRef(r)},8100:(e,t,s)=>{s.d(t,{A:()=>c});var a=s(5043),r=s(7591),l=s(7950),n=s(579);const i=e=>{let{isOpen:t,onClose:s,title:i,children:c,size:o="md",footer:d,closeOnEsc:m=!0,closeOnBackdropClick:x=!0,showCloseButton:u=!0,centered:h=!0,className:p="",bodyClassName:f="",headerClassName:g="",footerClassName:v="",backdropClassName:j="",testId:y}=e;const b=(0,a.useRef)(null);if((0,a.useEffect)((()=>{const e=e=>{m&&"Escape"===e.key&&s()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="auto"}}),[t,s,m]),(0,a.useEffect)((()=>{if(!t||!b.current)return;const e=b.current.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(0===e.length)return;const s=e[0],a=e[e.length-1],r=e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===s&&(a.focus(),e.preventDefault()):document.activeElement===a&&(s.focus(),e.preventDefault()))};return document.addEventListener("keydown",r),s.focus(),()=>{document.removeEventListener("keydown",r)}}),[t]),!t)return null;const w=(0,n.jsxs)(a.Fragment,{children:[(0,n.jsx)("div",{className:`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${j}`,onClick:x?s:void 0,"data-testid":`${y}-backdrop`}),(0,n.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,n.jsx)("div",{className:`flex min-h-full items-${h?"center":"start"} justify-center p-4 text-center`,children:(0,n.jsxs)("div",{ref:b,className:`${{xs:"max-w-xs",sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[o]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${p}`,onClick:e=>e.stopPropagation(),"data-testid":y,children:[(0,n.jsxs)("div",{className:`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${g}`,children:["string"===typeof i?(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:i}):i,u&&(0,n.jsx)("button",{type:"button",className:"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1",onClick:s,"aria-label":"Close modal","data-testid":`${y}-close-button`,children:(0,n.jsx)(r.A,{className:"h-6 w-6"})})]}),(0,n.jsx)("div",{className:`px-6 py-4 ${f}`,children:c}),d&&(0,n.jsx)("div",{className:`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${v}`,children:d})]})})})]});return(0,l.createPortal)(w,document.body)},c=(0,a.memo)(i)},8267:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(5043);function r(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"}))}const l=a.forwardRef(r)},8662:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(5043);function r(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}const l=a.forwardRef(r)},8682:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(5043);function r(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}const l=a.forwardRef(r)},9248:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(5043);function r(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const l=a.forwardRef(r)},9531:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(5043);function r(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const l=a.forwardRef(r)},9850:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(5043);function r(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}const l=a.forwardRef(r)}}]);
//# sourceMappingURL=584.6c8ad16d.chunk.js.map